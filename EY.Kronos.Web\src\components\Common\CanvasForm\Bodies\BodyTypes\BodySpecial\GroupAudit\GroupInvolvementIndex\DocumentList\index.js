import React, {useEffect, useState, useCallback} from 'react';
import {isEmpty} from 'lodash';
import {useDispatch, useSelector} from 'react-redux';
import Search from '@ey/canvascoreservices/Search';

import {useAresUrlParameters} from '../../../../../../../../../util/Ares/customHooks';
import {
	extendedPagingOptions,
	documentTypes,
	loadingIndicatorTypes,
	ExpandValues,
	clientSideEventNames,
	formBodyType,
	ValidationGroup,
	documentTypesWithDocumentGroups
} from '../../../../../../../../../util/uiconstants';
import {labels, currentResource, chunk} from '../../../../../../../../../util/utils';
import Utility from '../../../../../../../../../util/utilityfunctions';
import {useDocumentId} from '../../../../../../../../../util/customHooks';

import {getAllComponents} from '../../../../../../../../../actions/GroupAudit/GroupComponents';
import {getEntityDocuments} from '../../../../../../../../../actions/Document/entityDocumentActions';
import {getAllScopes} from '../../../../../../../../../actions/GroupAudit/GroupScopes';
import {getEmbeddedDocumentValidations} from '../../../../../../../../../actions/ValidationControl/validationcontrolactions';

import HelixIntegrationModalContainer from '../../../../../../../../Main/Ares/Helix/HelixIntegrationModal/index';
import Fetch from '../../../../../../../Fetch/fetch';
import Loader from '../../../../../../../LoadingIndicator/LoadingIndicator';
import Pagination from '../../../../../../../Pagination/Pagination';
import AresSpecialBodyContainer from '../../../../../../Bodies/BodyTypes/AresSpecialBodyContainer';
import GenerateGroupInvolvementModal from '../../Common/GenerateGroupInvolvementModal';
import LinkSeparator from '../../../Common/LinkSeparator';
import ManageComponentModal from '../../Common/ManageComponentModal';
import CreateGroupInvolvementDocModal from '../CreateGroupInvolvementDocModal/Index';
import ComponentTile from './ComponentTile';
import Style from './StyledDocumentList';

export default function DocumentList({
	bodyContent,
	headerId,
	sectionId,
	bodyId,
	updateBodyOptions,
	getBodiesBySectionId,
	isSnapshotView,
	showGuidance
}) {
	const {taskId} = useAresUrlParameters();
	const dispatch = useDispatch();

	const engagementId = useSelector((store) => store.engagementId);
	const documentId = useDocumentId();
	const components = useSelector(({groupAudit}) => groupAudit?.components);
	const scopes = useSelector(({groupAudit}) => groupAudit?.scopes);
	const entityDocuments = useSelector((store) => store.entityDocuments);
	const task = useSelector((store) => {
		if (store.aresTaskDetails && store.aresTaskDetails.hasOwnProperty(taskId)) {
			return store.aresTaskDetails[taskId];
		}

		return null;
	});

	const [isLoading, setIsLoading] = useState(true);
	const [searchText, setSearchText] = useState('');
	const [doSearch, setDoSearch] = useState(false);
	const [isFirstCall, setIsFirstCall] = useState(true);
	const [paging, setPaging] = useState({
		currentPage: 1,
		pageSize: extendedPagingOptions.options[0],
		totalPages: 1
	});
	const [searchComponents, setSearchComponents] = useState(null);
	const [showModal, setShowModal] = useState(false);
	const [requestValidation, setRequestValidation] = useState(true);

	const handleOnPageSizeChange = (pageSize) => {
		setRequestValidation(false);
		setPaging({...paging, pageSize, currentPage: 1});
		setDoSearch(true);
	};

	const handleOnPageChange = (pageNumber) => {
		setRequestValidation(false);
		setPaging({...paging, currentPage: pageNumber});
		setDoSearch(true);
	};

	const fetch = useCallback(() => {
		if (isFirstCall || (!isFirstCall && doSearch)) {
			getAccountsForBody();
		}
	}, [engagementId, paging.currentPage, paging.pageSize, doSearch, dispatch]);

	useEffect(() => {
		if (scopes === null) {
			dispatch(getAllScopes(engagementId)).then(() => setIsLoading(false));
		} else {
			setIsLoading(false);
		}
	}, []);

	useEffect(() => {
		if (entityDocuments && entityDocuments?.allIds?.length > 0) {
			const ids = entityDocuments.allIds?.filter((id) => {
				const entityDocument = entityDocuments.byId[id];
				return entityDocument.documentTypeId === documentTypes.GROUP_INVOLVEMENT_INDIVIDUAL_COMPONENT;
			});

			const embeddedDocuments = ids?.map((id) => {
				return entityDocuments && entityDocuments.byId && entityDocuments.byId[id];
			});

			const embeddedDocumentIds = embeddedDocuments?.map((doc) => doc.id);

			if (embeddedDocumentIds && embeddedDocumentIds.length > 0) {
				getEmbeddedValidations(embeddedDocumentIds);
			}
		}
	}, [entityDocuments]);

	const getEmbeddedValidations = (embeddedDocumentIds) => {
		const docTypeId = documentTypes.GROUP_INVOLVEMENT_INDIVIDUAL_COMPONENT;
		const documentGroups = documentTypesWithDocumentGroups.find((x) => x.documentTypeId == docTypeId);

		dispatch(
			getEmbeddedDocumentValidations(
				engagementId,
				[{id: documentId, documentTypeId: docTypeId}],
				formBodyType.GroupInvolvementGroupInvolvementIndex,
				ValidationGroup.GuidedWorkFlow,
				embeddedDocumentIds,
				documentGroups?.documentGroupId
			)
		);
	};

	const getAccountsForBody = () => {
		setIsLoading(true);
		getComponents();
	};

	const onChangeSearch = (value) => {
		setSearchText(value);
	};

	const onSearch = () => {
		const filtered = components?.data?.filter((c) => c.name.toLowerCase().includes(searchText.toLowerCase()));
		setSearchComponents(filtered);
		setPaging({...paging, currentPage: 1});
		setDoSearch(false);
	};

	const onSearchClear = () => {
		setSearchText('');
		setSearchComponents(null);
		setDoSearch(false);
		setPaging({...paging, currentPage: 1});
	};

	const onCreateDocument = () => {
		getDocuments(true);
	};

	const getComponents = () => {
		if (Utility.isValidNumber(paging.currentPage)) {
			const pagingValues = {
				page: paging.currentPage,
				pageSize: paging.pageSize
			};
			dispatch(getAllComponents(engagementId, ExpandValues.AllExceptReference, pagingValues)).then(() => {
				getDocuments();
			});
		}
	};

	const getDocuments = (getVal) => {
		dispatch(getEntityDocuments(engagementId, documentTypes.GROUP_INVOLVEMENT_INDIVIDUAL_COMPONENT)).finally(() => {
			setIsLoading(false);
			setIsFirstCall(false);
			setDoSearch(false);
			getVal && window.dispatchEvent(new CustomEvent(clientSideEventNames.getValidations));
		});
	};

	useEffect(() => {
		const totalPages = Math.ceil(components?.parameters?.totalEntityCount / paging.pageSize);
		setPaging({...paging, totalPages});
	}, [paging.currentPage, paging.pageSize, components?.parameters?.totalEntityCount, paging]);

	const renderCreateGroupInvolvementDocModal = () => {
		return <CreateGroupInvolvementDocModal />;
	};

	const renderToolbar = () => {
		const renderManageComponents = () => {
			const onHideModal = () => {
				setShowModal(false);
				window.dispatchEvent(new CustomEvent(clientSideEventNames.getValidations));
			};

			const modalProps = {
				showModal,
				setShowModal,
				onHideModal,
				components: components?.data,
				scopes,
				engagementId,
				size: 'medium AresModal editaccounts-modal group-audit-form',
				className: 'manage-component'
			};

			return <ManageComponentModal {...modalProps} />;
		};
		
		const renderGenerateGroupInvolvement = () => {
			if (!task || !task.taskId) {
				console.log('waiting on taskID');
				return <Loader {...loaderProps} />;
			}
			const isButtonDisabled =
				entityDocuments.allIds.filter(
					(id) => entityDocuments.byId[id].documentTypeId === documentTypes.GROUP_INVOLVEMENT_INDIVIDUAL_COMPONENT
				).length === 0;

			const modalProps = {
				components: components.data?.filter((data) => data?.relatedDocuments?.length > 0),
				engagementId,
				task,
				isButtonDisabled
			};

			return <GenerateGroupInvolvementModal {...modalProps} />;
		};

		const searchProps = {
			id: 'ComponentsSearchbox',
			styleName: 'ComponentsSearchWrapper',
			value: searchText,
			placeholder: labels.placeholderForSearch,
			searchHoverText: labels.placeholderForSearch,
			clearHoverText: labels.clearHoverText,
			onChange: onChangeSearch,
			onSearchButtonClick: onSearch,
			onEnter: onSearch,
			onClear: onSearchClear
		};

		return (
			<section className="ComponentFilterWrapper">
				<section className="ComponentsSearchbox">
					<Search {...searchProps} />
				</section>
				<section className="ComponentsActions">
					{renderManageComponents()}
					<LinkSeparator />
					{renderGenerateGroupInvolvement()}
				</section>
			</section>
		);
	};

	const renderTiles = () => {
		const componentsList = searchComponents || components?.data;
		if (searchComponents?.length === 0) {
			return (
				<section className="ComponentOverviewContainer noComponentRecords">
					{Utility.createRichTextElement(currentResource.groupStructure.noComponentsFound)}
				</section>
			);
		}

		if (componentsList?.length > 0) {
			const paginationProps = {
				className: 'account-paging',
				optionsPerPage: extendedPagingOptions.options,
				page: paging.currentPage,
				pageSize: paging.pageSize,
				pageCount: paging.totalPages,
				onPageSizeChanged: handleOnPageSizeChange,
				onPageChanged: handleOnPageChange
			};

			return (
				<>
					<section className="AccountOverviewContainer customScrollbar">
						{componentsList?.map((component) => {
							const componentTileProps = {
								key: component.id,
								component,
								onCreateDocument: onCreateDocument
							};
							return <ComponentTile {...componentTileProps} />;
						})}
					</section>
					<section className="paging">
						<Pagination {...paginationProps} />
					</section>
				</>
			);
		}
		return (
			<section className="ComponentOverviewContainer noComponentRecords">
				{Utility.createRichTextElement(currentResource.groupInvolvement.NoComponentsAvailables)}
			</section>
		);
	};

	const loaderProps = {
		show: true,
		indicatorType: loadingIndicatorTypes.progress,
		label: labels.saving,
		view: 'inlineView',
		styleName: 'LoadingIndicator'
	};

	const aresSpecialBodyContainerProps = {
		bodyContent,
		headerId,
		sectionId,
		bodyId,
		updateBodyOptions,
		getBodiesBySectionId,
		isSnapshotView,
		showGuidance,
		showDescription: false
	};

	return (
		<Fetch fetch={fetch}>
			<Style className="StyledComponentList">
				<HelixIntegrationModalContainer>
					{isLoading ? (
						<Loader {...loaderProps} />
					) : (
						<>
							<AresSpecialBodyContainer {...aresSpecialBodyContainerProps}>
								{renderToolbar()}
								{renderCreateGroupInvolvementDocModal()}
								{renderTiles()}
							</AresSpecialBodyContainer>
						</>
					)}
				</HelixIntegrationModalContainer>
			</Style>
		</Fetch>
	);
}
