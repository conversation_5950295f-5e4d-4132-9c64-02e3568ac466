import React, {useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import PropTypes from 'prop-types';
import OutputText from '@ey/canvascoreservices/OutputText';
import Modal from '@ey/canvascoreservices/Modal';
import Form from '@ey/canvascoreservices/Form';
import IconButton from '@ey/canvascoreservices/IconButton';

import {currentResource} from '../../../../../../../../../util/utils';
import {features} from '../../../../../../../../../util/uiconstants';
import Utility from '../../../../../../../../../util/utilityfunctions';

import {setIsModalOpen} from '../../../../../../../../../actions/setismodalopen';
import {clearErrors, raiseSuccessMessage} from '../../../../../../../../../actions/erroractions';
import {postInvolvementPackages} from '../../../../../../../../../actions/GroupAudit/GroupTasks';
import {setFormV2ToasterVisibility} from '../../../../../../../../../actions/toastactions';

import LoadingIndicator from '../../../../../../../LoadingIndicator/LoadingIndicator';
import {loadStorage, saveStorage, clearItem} from '../../../../../../../../Common/localStorage';
import ErrorSummary from '../../../../../../../../Common/ErrorSummary/ErrorSummary';
import CheckboxOption from '../SelectWithCheckbox/CheckboxOption';

import StyledGenerateModal from './StyledGenerateModal';
import {COMPONENT_IDS, DOCUMENT_NAME} from './utils/constant';

const GenerateGroupInvolvementModal = ({
	components = {id: 0, name: ''},
	engagementId = '',
	task = {taskId: '', taskName: ''},
	isButtonDisabled = false
}) => {
	const [showModal, setShowModal] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const labels = currentResource.groupInvolvement;

	const isModalOpen = useSelector(({isModalOpen}) => isModalOpen);

	// taskName limited to 89 chars to not go over 115 chars limit.
	const initialValues = {
		[COMPONENT_IDS]: [],
		[DOCUMENT_NAME]: `${labels.documentName.replace('{taskName}', task && Utility.trimText(task.taskName, 89))}`
	};

	const schema = {
		[COMPONENT_IDS]: {required: true},
		[DOCUMENT_NAME]: {required: false}
	};

	const dispatch = useDispatch();

	const setCacheKeyWithTTL = () => {
		const ttl = new Date();
		ttl.setMinutes(ttl.getMinutes() + 10);

		const ttlObject = {
			date: ttl
		};

		saveStorage(features.INVOLVEMENT_PACKAGE_IN_PROGRESS, JSON.stringify(ttlObject));
	};

	const isPackageInProgress = () => {
		const cacheValue = loadStorage(features.INVOLVEMENT_PACKAGE_IN_PROGRESS);

		if (cacheValue) {
			const ttl = JSON.parse(cacheValue);

			const now = new Date();
			const ttlDate = new Date(ttl?.date);

			if (ttl?.date && now.getTime() < ttlDate) {
				dispatch(
					setFormV2ToasterVisibility(true, 'info', currentResource.groupInstructions.ALRAPackageInProgressToastMessage)
				);
				return false;
			} else {
				// Time got expired then process should continue.
				clearItem(features.INVOLVEMENT_PACKAGE_IN_PROGRESS);
				return true;
			}
		}

		return true;
	};

	const renderTrigger = () => {
		const onClick = () => {
			setShowModal(isPackageInProgress());
			dispatch(setIsModalOpen(isPackageInProgress()));
		};

		const props = {
			label: labels.generateGroupInvolvementCommunications,
			showLabel: true,
			variant: 'text-alt',
			disabled: isButtonDisabled,
			onClick
		};

		return <IconButton {...props} />;
	};

	const renderText = () => {
		const outputTextProps = {
			size: 'L',
			weight: 'light'
		};

		return (
			<section>
				<OutputText {...outputTextProps}>
					{Utility.createRichTextElement(labels.generateGroupInvolvementInstructionalText)}
				</OutputText>
			</section>
		);
	};

	const renderLoading = () => <LoadingIndicator show />;

	const renderComponentTeams = (componentIds, handleChange) => {
		const outputTextProps = {
			size: 'L',
			weight: 'bold',
			className: 'component-title'
		};

		const handlerOnCheckAll = () => {
			const list = hasAllComponentsChecked() ? [] : components.map((c) => c.id);
			handleChange(COMPONENT_IDS, list);
		};

		const handlerOnCheck = (id) => {
			let newList = [];

			newList = componentIds.includes(id) ? componentIds.filter((c) => c !== id) : [...componentIds, id];

			handleChange(COMPONENT_IDS, newList);
		};

		const hasComponentsChecked = () => componentIds.length > 0;

		const hasAllComponentsChecked = () => componentIds.length === components.length;

		const isIndeterminate = () => {
			const hasChecked = hasComponentsChecked();
			const hasAllChecked = hasAllComponentsChecked();

			return hasChecked && !hasAllChecked;
		};

		const checkAllProp = {
			id: 0,
			label: hasAllComponentsChecked() ? labels.unselectAll : labels.selectAll,
			selected: hasAllComponentsChecked(),
			indeterminate: isIndeterminate(),
			onSelect: handlerOnCheckAll
		};

		return (
			<section className="component-teams">
				<OutputText {...outputTextProps}>{labels.componentTeams}</OutputText>
				<div className="checkbox-wrapper">
					<CheckboxOption {...checkAllProp} />
					{components.map((c) => {
						const checkProps = {
							id: c.id,
							label: c.name,
							selected: componentIds.includes(c.id),
							onSelect: handlerOnCheck
						};

						return <CheckboxOption {...checkProps} key={`component-item-${c.id}`} />;
					})}
				</div>
			</section>
		);
	};

	const renderModal = (values, handleChange, handleSubmit, handleReset) => {
		const onCloseModal = () => {
			handleReset();
			dispatch(clearErrors())
				.then(() => isModalOpen && dispatch(setIsModalOpen(false)))
				.then(() => setShowModal(false));
		};

		const handleCreate = () => {
			console.log('TASKid-gen', task.taskId);
			if (values[COMPONENT_IDS].length === 0) {
				dispatch(raiseSuccessMessage(labels.noComponentsSelectedErrorMessage));
			}

			handleSubmit(() => {
				handleReset();
				isModalOpen && dispatch(setIsModalOpen(false));
				setShowModal(false);
				setIsLoading(false);
				setCacheKeyWithTTL();
				dispatch(
					setFormV2ToasterVisibility(true, 'success', currentResource.groupInstructions.ALRAPackageSuccessToastMessage)
				);
			});
		};

		const modalProps = {
			show: showModal,
			headerText: labels.generateGroupInvolvementCommunications,
			noPaddingOnBody: true,
			size: 'medium AresModal editaccounts-modal group-audit-form',
			showFooter: true,
			showFooterDivider: true,
			fullHeight: true,
			onClose: onCloseModal,
			onCancelClick: onCloseModal,
			onOkClick: handleCreate,
			labels: {
				modalConfirmBtnLabel: labels.modalConfirmBtnLabel,
				modalCancelBtnLabel: labels.modalCancelBtnLabel,
				modalCloseBtnTitletip: labels.modalCloseBtnTitletip
			}
		};

		return (
			<Modal {...modalProps}>
				<StyledGenerateModal className="customScrollbar">
					<ErrorSummary />
					{isLoading && renderLoading()}
					{renderText()}
					{renderComponentTeams(values[COMPONENT_IDS], handleChange)}
				</StyledGenerateModal>
			</Modal>
		);
	};

	const props = {
		initialValues,
		schema,
		onSubmit: (values, callback) => {
			setIsLoading(true);

			dispatch(postInvolvementPackages(engagementId, task.taskId, values))
				.then(() => {
					dispatch(clearErrors());
					callback();
				})
				.catch(() => {
					setIsLoading(false);
				});
		}
	};
	return (
		<>
			{renderTrigger()}
			<Form {...props}>
				{({values, errors, handleChange, handleSubmit, handleReset, isFormValid}) => {
					return showModal && renderModal(values, handleChange, handleSubmit, handleReset);
				}}
			</Form>
		</>
	);
};

GenerateGroupInvolvementModal.propTypes = {
	components: PropTypes.objectOf(
		PropTypes.shape({
			id: PropTypes.number,
			name: PropTypes.string
		})
	),
	engagementId: PropTypes.string,
	task: PropTypes.objectOf(
		PropTypes.shape({
			taskId: PropTypes.string,
			taskName: PropTypes.string
		})
	),
	isButtonDisabled: PropTypes.bool
};

export default GenerateGroupInvolvementModal;
